import { getDb } from "@/lib/mongodb";
import { NextResponse } from "next/server";

export async function GET() {
  try {
    const db = await getDb();
    const collection = db.collection("listings");

    const listings = await collection.find({}).limit(100).toArray();
    if(listings.length === 0) {
      return NextResponse.json({ success: false, data: [] }, { status: 404 });
    }

    return NextResponse.json({ success: true, data: listings });
  } catch (error) {
    console.error("Error fetching listings:", error);
    return NextResponse.json({ success: false, error: "Failed to fetch listings" }, { status: 500 });
  }
}