import "./globals.css";
import { Metada<PERSON> } from "next";
import { Toaster } from "sonner";

export const metadata: Metadata = {
  metadataBase: new URL("https://helium-tpc.vercel.app"),
  title: "Helium AI x TPC",
  description: "Helium AI chatbot for renting properties in Bengaluru",
  icons: {
    icon: '/favicon.ico',
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className="dark dark:bg-zinc-950 dark:text-zinc-100" suppressHydrationWarning={true}>
        <Toaster position="top-center" richColors />
        {children}
      </body>
    </html>
  );
}
