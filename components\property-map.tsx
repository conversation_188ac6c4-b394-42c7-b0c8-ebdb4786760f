"use client"

import { useEffect, useRef, useState } from "react"
import { Loader } from "@googlemaps/js-api-loader"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { MapPin, Star, Wifi, Car, Coffee } from "lucide-react"
import { Skeleton } from "./ui/skeleton"

interface PropertyCards {
    availableFrom: number
    balconies: number
    balconyFacing: string
    baths: number
    cluster: string
    facing: string
    floor: number
    furnishing: string
    heliumId: string
    location: {
        type: string
        coordinates: Array<number | number[]>
    }
    maintenance: number
    rent: number
    rooms: number
    sizeSqft: number
    sno: number
    society: string
    societyAge: string
    tenantType: string[]
    thumbUrl: string
    totalFloors: number
    totalRent: number
    _id: string
}

// Declare Google Maps types
declare global {
    interface Window {
        google: any
    }
}

export default function PropertyMap() {
    const mapRef = useRef<HTMLDivElement>(null)
    const cardRef = useRef<HTMLDivElement>(null)
    const [map, setMap] = useState<any>(null)
    const [selectedHotel, setSelectedHotel] = useState<any | null>(null)
    const [hoveredHotel, setHoveredHotel] = useState<any | null>(null)
    const [isLoading, setIsLoading] = useState(true)
    const [markerPosition, setMarkerPosition] = useState<{ x: number; y: number } | null>(null)
    const [cardPosition, setCardPosition] = useState<{ x: number; y: number } | null>(null)
    const [userLocation, setUserLocation] = useState<{ lat: number; lng: number } | null>(null)

    const [properties, setProperties] = useState<PropertyCards[]>([])
    const [loading, setLoading] = useState(true)
    const [searchQuery, setSearchQuery] = useState<string>("") // Can be modified based on actual use case

    // Get user location
    const getUserLocation = () => {
        if (navigator.geolocation) {
            navigator.geolocation.getCurrentPosition(
                (position) => {
                    setUserLocation({
                        lat: position.coords.latitude,
                        lng: position.coords.longitude,
                    })
                },
                (error) => {
                    console.error("Error getting location:", error)
                    setUserLocation({ lat: 18.564836, lng: 73.830372 })
                }
            )
        } else {
            console.error("Geolocation is not supported by this browser.")
            setUserLocation({ lat: 18.564836, lng: 73.830372 })
        }
    }

    useEffect(() => {
        getUserLocation()
    }, [])

    // Fetch properties from API
    useEffect(() => {
        setLoading(true)
        fetch("/api/properties")
            .then((res) => res.json())
            .then((data) => {
                if (data.success) {
                    console.log(data)
                    setProperties(data.data)
                } else {
                    console.error(data.error)
                }
            })
            .catch((error) => {
                console.error("Error fetching properties:", error)
            })
            .finally(() => setLoading(false))
    }, [searchQuery])

    // Initialize Google Map
    useEffect(() => {
        const initMap = async () => {
            const loader = new Loader({
                apiKey: process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY || "YOUR_API_KEY_HERE",
                version: "weekly",
            })

            try {
                await loader.load()

                if (!window.google) {
                    throw new Error("Google Maps failed to load")
                }

                if (mapRef.current) {
                    const mapInstance = new window.google.maps.Map(mapRef.current, {
                        center: userLocation || { lat: 18.564836, lng: 73.830372 },
                        zoom: 12,
                        styles: [
                            {
                                featureType: "poi",
                                elementType: "labels",
                                stylers: [{ visibility: "off" }],
                            },
                        ],
                    })

                    new window.google.maps.Marker({
                        position: userLocation || { lat: 18.564836, lng: 73.830372 },
                        map: mapInstance,
                        title: "Your Location",
                        icon: {
                            path: window.google.maps.SymbolPath.CIRCLE,
                            scale: 8,
                            fillColor: "#3B82F6",
                            fillOpacity: 1,
                            strokeColor: "#FFFFFF",
                            strokeWeight: 2,
                        },
                    })

                    // Add property markers
                    properties.forEach((property) => {
                        const position = {
                            lat: property.location.coordinates[1] as number,
                            lng: property.location.coordinates[0] as number,
                        }

                        const marker = new window.google.maps.Marker({
                            position,
                            map: mapInstance,
                            title: property.society,
                            icon: {
                                path: window.google.maps.SymbolPath.CIRCLE,
                                scale: 0,
                                fillOpacity: 0,
                                strokeOpacity: 0,
                            },
                        })

                        const markerDiv = document.createElement("div")
                        markerDiv.className = "relative cursor-pointer"
                        markerDiv.innerHTML = `
                            <div class="bg-white rounded-lg shadow-lg px-3 py-1 text-sm font-semibold text-gray-800 hover:bg-blue-50 transition-colors">
                                ₹${property.rent.toLocaleString()}
                            </div>
                        `

                        const overlay = new window.google.maps.OverlayView()
                        overlay.onAdd = function () {
                            const panes = this.getPanes()
                            panes?.overlayMouseTarget.appendChild(markerDiv)
                        }

                        overlay.draw = function () {
                            const projection = this.getProjection()
                            const positionPixel = projection.fromLatLngToDivPixel(new window.google.maps.LatLng(position.lat, position.lng))
                            if (positionPixel) {
                                markerDiv.style.left = positionPixel.x - 30 + "px"
                                markerDiv.style.top = positionPixel.y - 20 + "px"
                                markerDiv.style.position = "absolute"
                            }
                        }

                        overlay.onRemove = () => {
                            if (markerDiv.parentNode) {
                                markerDiv.parentNode.removeChild(markerDiv)
                            }
                        }

                        overlay.setMap(mapInstance)

                        markerDiv.addEventListener("mouseenter", () => {
                            setHoveredHotel({
                                id: property._id,
                                name: property.society,
                                price: property.rent,
                                rating: 4.0,
                                coordinates: position,
                                image: property.thumbUrl || "/placeholder.svg",
                                description: `Rooms: ${property.rooms}, Size: ${property.sizeSqft} sqft`,
                                amenities: property.tenantType || [],
                                address: property.society + ", " + property.cluster,
                            })
                            const rect = mapRef.current?.getBoundingClientRect()
                            if (rect) {
                                const markerRect = markerDiv.getBoundingClientRect()
                                const markerCenterX = markerRect.left + markerRect.width / 2 - rect.left
                                const markerCenterY = markerRect.top + markerRect.height / 2 - rect.top
                                setMarkerPosition({ x: markerCenterX, y: markerCenterY })
                            }
                        })

                        markerDiv.addEventListener("mouseleave", () => {
                            setHoveredHotel(null)
                            setMarkerPosition(null)
                            setCardPosition(null)
                        })

                        markerDiv.addEventListener("click", () => {
                            setSelectedHotel({
                                id: property._id,
                                name: property.society,
                                price: property.rent,
                                rating: 4.0,
                                coordinates: position,
                                image: property.thumbUrl || "/placeholder.svg",
                                description: `Rooms: ${property.rooms}, Size: ${property.sizeSqft} sqft`,
                                amenities: property.tenantType || [],
                                address: property.society + ", " + property.cluster,
                            })
                            mapInstance.panTo(position)
                            mapInstance.setZoom(15)
                        })
                    })

                    setMap(mapInstance)
                    setIsLoading(false)
                }
            } catch (error) {
                console.error("Error loading Google Maps:", error)
                setIsLoading(false)
            }
        }

        if (userLocation && properties.length > 0) {
            initMap()
        }
    }, [userLocation, properties])

    // Calculate card position after hover
    useEffect(() => {
        if (hoveredHotel && markerPosition && cardRef.current && mapRef.current) {
            const rect = mapRef.current.getBoundingClientRect()
            const cardRect = cardRef.current.getBoundingClientRect()
            const cardWidth = cardRect.width
            const cardHeight = cardRect.height
            const padding = 20

            let x = markerPosition.x - cardWidth / 2
            let y = markerPosition.y - cardHeight - padding

            if (y < padding) {
                y = markerPosition.y + padding
            }

            if (x < padding) {
                x = padding
            } else if (x + cardWidth > rect.width - padding) {
                x = rect.width - cardWidth - padding
            }

            if (y + cardHeight > rect.height - padding) {
                y = markerPosition.y - cardHeight - padding
            }

            if (!cardPosition || cardPosition.x !== x || cardPosition.y !== y) {
                setCardPosition({ x, y })
            }
        }
    }, [hoveredHotel, markerPosition, cardPosition])

    const getAmenityIcon = (amenity: string) => {
        switch (amenity.toLowerCase()) {
            case "wifi":
                return <Wifi className="w-4 h-4" />
            case "parking":
                return <Car className="w-4 h-4" />
            case "restaurant":
            case "bar":
                return <Coffee className="w-4 h-4" />
            default:
                return <MapPin className="w-4 h-4" />
        }
    }

    return (
        <div className="relative w-fill h-[calc(100vh-90px)] overflow-hidden rounded-lg light">
            {isLoading && (
                <div className="absolute top-0 left-0 w-full h-full flex items-center justify-center bg-white dark:bg-zinc-950">
                    <Skeleton className="w-full h-full rounded-lg" />
                </div>
            )}

            <div ref={mapRef} className="w-full h-full" />

            {hoveredHotel && markerPosition && (
                <div
                    ref={cardRef}
                    className="absolute z-20 pointer-events-none light"
                    style={{
                        left: `${cardPosition?.x || markerPosition.x}px`,
                        top: `${cardPosition?.y || markerPosition.y}px`,
                    }}
                >
                    <Card className="w-80 shadow-xl p-2">
                        <CardHeader className="pb-2 p-0">
                            <div className="flex justify-between items-start">
                                <div>
                                    <CardTitle className="text-lg">{hoveredHotel.name}</CardTitle>
                                    <CardDescription className="flex items-center gap-1">
                                        <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                                        {hoveredHotel.rating}
                                    </CardDescription>
                                </div>
                                <Badge variant="secondary" className="text-lg font-bold">
                                    ₹{hoveredHotel.price.toLocaleString()}
                                </Badge>
                            </div>
                        </CardHeader>
                        <CardContent className="p-0">
                            <p className="text-sm text-gray-600 mb-3">{hoveredHotel.description}</p>
                            <div className="flex flex-wrap gap-2 mb-2">
                                {hoveredHotel.amenities.slice(0, 4).map((amenity: string) => (
                                    <div key={amenity} className="flex items-center gap-1 text-xs bg-gray-100 px-2 py-1 rounded-full">
                                        {getAmenityIcon(amenity)}
                                        {amenity}
                                    </div>
                                ))}
                            </div>
                            <p className="text-xs text-gray-500">{hoveredHotel.address}</p>
                        </CardContent>
                    </Card>
                </div>
            )}

            {selectedHotel && (
                <div className="absolute md:max-w-md bottom-4 left-4 md:left-auto right-4 z-20">
                    <Card className="shadow-xl p-2">
                        <CardHeader className="p-0">
                            <div className="flex justify-between items-start">
                                <div>
                                    <CardTitle className="text-xl">{selectedHotel.name}</CardTitle>
                                    <CardDescription className="flex items-center gap-1 text-base">
                                        <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                                        {selectedHotel.rating} • {selectedHotel.address}
                                    </CardDescription>
                                </div>
                                <div className="text-right">
                                    <Badge variant="default" className="text-xl font-bold mb-2">
                                        ₹{selectedHotel.price.toLocaleString()}
                                    </Badge>
                                    <button
                                        onClick={() => setSelectedHotel(null)}
                                        className="block text-sm text-gray-500 hover:text-gray-700"
                                    >
                                        Close
                                    </button>
                                </div>
                            </div>
                        </CardHeader>
                        <CardContent className="p-0">
                            <div className="grid md:grid-cols-2 gap-4">
                                <img
                                    src={selectedHotel.image || "/placeholder.svg"}
                                    alt={selectedHotel.name}
                                    className="w-full h-48 object-cover rounded-md"
                                />
                                <div>
                                    <p className="text-gray-600 mb-4">{selectedHotel.description}</p>
                                    <div className="grid grid-cols-2 gap-2">
                                        {selectedHotel.amenities.map((amenity: string) => (
                                            <div key={amenity} className="flex items-center gap-2 text-sm">
                                                {getAmenityIcon(amenity)}
                                                {amenity}
                                            </div>
                                        ))}
                                    </div>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                </div>
            )}
        </div>
    )
}
