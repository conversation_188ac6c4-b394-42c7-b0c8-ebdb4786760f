"use client";

import { useRef } from "react";
import { Message } from "@/components/message";
import { useScrollToBottom } from "@/components/use-scroll-to-bottom";
import { motion } from "framer-motion";
import { HeliumIcon, MasonryIcon, VercelIcon } from "@/components/icons";
import Link from "next/link";
import { useChat } from "ai/react";

export default function ChatWindow() {
    const { messages, handleSubmit, input, setInput, append } = useChat();

    const inputRef = useRef<HTMLInputElement>(null);
    const [messagesContainerRef, messagesEndRef] =
        useScrollToBottom<HTMLDivElement>();

    const suggestedActions = [
        {
            title: "Find me 2 BHK",
            label: "in Varthur",
            action: "find me 2 BHK in Varthur",
        },
        {
            title: "What orders",
            label: "have shipped?",
            action: "what orders have shipped?",
        },
    ];

    return (
        <div className="flex flex-row justify-center pb-6 h-screen bg-white dark:bg-zinc-950 w-[450px]">
            <div className="flex flex-col justify-between gap-4">
                <div
                    ref={messagesContainerRef}
                    className="flex flex-col gap-6 h-full items-center pr-4 overflow-y-auto
  [&::-webkit-scrollbar]:w-1
  [&::-webkit-scrollbar-track]:rounded-full
  [&::-webkit-scrollbar-track]:bg-gray-100
  [&::-webkit-scrollbar-thumb]:rounded-full
  [&::-webkit-scrollbar-thumb]:bg-gray-300
  dark:[&::-webkit-scrollbar-track]:bg-neutral-700
  dark:[&::-webkit-scrollbar-thumb]:bg-neutral-500"
                >
                    {messages.length === 0 && (
                        <motion.div className="h-[350px] px-4 w-full md:px-0 pt-8">
                            <div className="border rounded-lg p-6 flex flex-col gap-4 text-zinc-500 text-sm dark:text-zinc-400 dark:border-zinc-800">
                                <p className="flex flex-row justify-center gap-4 items-center text-zinc-900 dark:text-zinc-50">
                                    <VercelIcon size={16} />
                                    <span>+</span>
                                    <HeliumIcon />
                                </p>
                                <p>
                                    This is Helium Homes rental property assistant, powered by Vercel AI SDK. this bot can help you quickly find your dream rental property by asking the right clarifying questions and matching them with the most suitable listings from the database.
                                </p>
                            </div>
                        </motion.div>
                    )}

                    {messages.map((message) => (
                        <Message
                            key={message.id}
                            role={message.role}
                            content={message.content}
                            toolInvocations={message.toolInvocations}
                        ></Message>
                    ))}
                    <div ref={messagesEndRef} className="h-0" />
                </div>

                {messages.length === 0 && (
                    <div className="grid sm:grid-cols-2 gap-2 w-full px-4 md:px-0 mx-auto mb-4">
                        {suggestedActions.map((suggestedAction, index) => (
                            <motion.div
                                initial={{ opacity: 0, y: 20 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ delay: 0.05 * index }}
                                key={index}
                                className={index > 1 ? "hidden sm:block" : "block"}
                            >
                                <button
                                    onClick={() => {
                                        append({
                                            role: "user",
                                            content: suggestedAction.action,
                                        });
                                    }}
                                    className="w-full text-left border border-zinc-200 dark:border-zinc-800 text-zinc-800 dark:text-zinc-300 rounded-lg p-2 text-sm hover:bg-zinc-100 dark:hover:bg-zinc-800 transition-colors flex flex-col"
                                >
                                    <span className="font-medium">{suggestedAction.title}</span>
                                    <span className="text-zinc-500 dark:text-zinc-400">
                                        {suggestedAction.label}
                                    </span>
                                </button>
                            </motion.div>
                        ))}
                    </div>
                )}


                <form
                    className="flex flex-row gap-2 relative items-center  w-full "
                    onSubmit={handleSubmit}
                >
                    <input
                        ref={inputRef}
                        className="bg-zinc-100 rounded-md px-3 py-1.5 w-full outline-none dark:bg-zinc-800 text-zinc-800 dark:text-zinc-300"
                        placeholder="Send a message..."
                        value={input}
                        onChange={(event) => {
                            setInput(event.target.value);
                        }}
                    />
                    <button
                        className="bg-zinc-800 rounded-md px-3 py-1.5 outline-none dark:bg-zinc-200 text-zinc-200 dark:text-zinc-800"
                        onClick={handleSubmit}
                    >
                        Send
                    </button>
                </form>
            </div>
        </div>
    );
}
